"use client";

import { useState } from "react";
import {
  Upload,
  Camera,
  Calendar,
  MapPin,
  User,
  FileText,
  CheckCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { FormData } from "../page";
import { cn } from "@/lib/utils";

interface GoodMoralFormProps {
  onSubmit: (data: FormData) => void;
  initialData: FormData;
}

export function GoodMoralForm({ onSubmit, initialData }: GoodMoralFormProps) {
  const [formData, setFormData] = useState<FormData>(initialData);
  const [photo, setPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string>("");
  const [isDragOver, setIsDragOver] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to check if a step is completed
  const isStepCompleted = (stepId: number) => {
    switch (stepId) {
      case 1: // Photo & Personal Info
        return (
          formData.image &&
          formData.firstName &&
          formData.lastName &&
          formData.age &&
          formData.barangayAddress
        );
      case 2: // Location Details
        return formData.province && formData.municipality;
      case 3: // Document & Official
        return (
          formData.day &&
          formData.month &&
          formData.year &&
          formData.mayor &&
          formData.ctcNo &&
          formData.orNo
        );
      default:
        return false;
    }
  };

  // Get current active step based on completion
  const getCurrentStep = () => {
    if (!isStepCompleted(1)) return 1;
    if (!isStepCompleted(2)) return 2;
    if (!isStepCompleted(3)) return 3;
    return 3; // All completed
  };

  const currentStep = getCurrentStep();

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (file: File) => {
    if (file.type.startsWith("image/")) {
      setPhoto(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPhotoPreview(result);
        handleInputChange("image", result);
      };
      reader.readAsDataURL(file);
      toast.success("Photo uploaded successfully!");
    } else {
      toast.error("Please upload a valid image file");
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) handlePhotoUpload(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    toast.success("Good Moral Certificate application submitted successfully!");
    onSubmit(formData);
  };

  const steps = [
    { id: 1, title: "Photo & Personal Info", icon: User },
    { id: 2, title: "Location Details", icon: MapPin },
    { id: 3, title: "Document & Official", icon: FileText },
  ];

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-foreground text-2xl font-semibold">
            Good Moral Certificate Application
          </h1>
          <p className="text-muted-foreground">
            Fill out the form to apply for your certificate
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {steps.map((step, index) => {
              const isCompleted = isStepCompleted(step.id);
              const isActive = currentStep === step.id;

              return (
                <div key={step.id} className="flex items-center">
                  <div
                    className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                    ${
                      isCompleted
                        ? "bg-primary border-primary text-primary-foreground"
                        : isActive
                        ? "bg-primary/10 border-primary text-primary"
                        : "border-border text-muted-foreground"
                    }
                  `}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <step.icon className="w-5 h-5" />
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`
                      w-16 h-0.5 mx-2 transition-all duration-300
                      ${isCompleted ? "bg-primary" : "bg-border"}
                    `}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Photo Upload */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Camera className="w-5 h-5" />
                Upload 2x2 Photo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`
                  relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
                  ${
                    isDragOver
                      ? "border-primary bg-primary/10"
                      : "border-border hover:border-primary/50"
                  }
                `}
                onDrop={handleDrop}
                onDragOver={(e) => {
                  e.preventDefault();
                  setIsDragOver(true);
                }}
                onDragLeave={() => setIsDragOver(false)}
              >
                {photoPreview ? (
                  <div className="space-y-4">
                    <img
                      src={photoPreview}
                      alt="Preview"
                      className="w-32 h-32 object-cover rounded-lg mx-auto border-2 border-border"
                    />
                    <p className="text-muted-foreground">
                      Photo uploaded successfully
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        document.getElementById("photo-input")?.click()
                      }
                    >
                      Change Photo
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                    <div>
                      <p className="text-foreground mb-2">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-muted-foreground text-sm">
                        2x2 photo required (JPG, PNG)
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() =>
                        document.getElementById("photo-input")?.click()
                      }
                    >
                      Browse Files
                    </Button>
                  </div>
                )}
                <input
                  id="photo-input"
                  type="file"
                  accept="image/*"
                  onChange={(e) =>
                    e.target.files?.[0] && handlePhotoUpload(e.target.files[0])
                  }
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-primary flex items-center gap-2">
                <User className="w-5 h-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-card-foreground">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="Enter first name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="middleName" className="text-card-foreground">
                    Middle Name
                  </Label>
                  <Input
                    id="middleName"
                    value={formData.middleName}
                    onChange={(e) =>
                      handleInputChange("middleName", e.target.value)
                    }
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="Enter middle name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-card-foreground">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="Enter last name"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="age" className="text-card-foreground">
                    Age
                  </Label>
                  <Input
                    id="age"
                    type="number"
                    value={formData.age}
                    onChange={(e) => handleInputChange("age", e.target.value)}
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="Enter age"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="barangayAddress"
                    className="text-card-foreground"
                  >
                    Barangay Address
                  </Label>
                  <Input
                    id="barangayAddress"
                    value={formData.barangayAddress}
                    onChange={(e) =>
                      handleInputChange("barangayAddress", e.target.value)
                    }
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="e.g., Brgy. Mohon"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-primary flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="province" className="text-card-foreground">
                    Province
                  </Label>
                  <Select
                    value={formData.province}
                    onValueChange={(value) =>
                      handleInputChange("province", value)
                    }
                  >
                    <SelectTrigger className="bg-input/30 border-border text-foreground">
                      <SelectValue placeholder="Select province" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      <SelectItem value="leyte">Leyte</SelectItem>
                      <SelectItem value="cebu">Cebu</SelectItem>
                      <SelectItem value="bohol">Bohol</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="municipality"
                    className="text-card-foreground"
                  >
                    Municipality
                  </Label>
                  <Select
                    value={formData.municipality}
                    onValueChange={(value) =>
                      handleInputChange("municipality", value)
                    }
                  >
                    <SelectTrigger className="bg-input/30 border-border text-foreground">
                      <SelectValue placeholder="Select municipality" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      <SelectItem value="tanauan">Tanauan</SelectItem>
                      <SelectItem value="tacloban">Tacloban</SelectItem>
                      <SelectItem value="ormoc">Ormoc</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Document Details & Official Use */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-primary flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Document Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="day" className="text-card-foreground">
                      Day
                    </Label>
                    <Input
                      id="day"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.day}
                      onChange={(e) => handleInputChange("day", e.target.value)}
                      className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                      placeholder="17"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="month" className="text-card-foreground">
                      Month
                    </Label>
                    <Select
                      value={formData.month}
                      onValueChange={(value) =>
                        handleInputChange("month", value)
                      }
                    >
                      <SelectTrigger className="bg-input/30 border-border text-foreground">
                        <SelectValue placeholder="July" />
                      </SelectTrigger>
                      <SelectContent className="bg-popover border-border">
                        <SelectItem value="january">January</SelectItem>
                        <SelectItem value="february">February</SelectItem>
                        <SelectItem value="march">March</SelectItem>
                        <SelectItem value="april">April</SelectItem>
                        <SelectItem value="may">May</SelectItem>
                        <SelectItem value="june">June</SelectItem>
                        <SelectItem value="july">July</SelectItem>
                        <SelectItem value="august">August</SelectItem>
                        <SelectItem value="september">September</SelectItem>
                        <SelectItem value="october">October</SelectItem>
                        <SelectItem value="november">November</SelectItem>
                        <SelectItem value="december">December</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="year" className="text-card-foreground">
                      Year
                    </Label>
                    <Input
                      id="year"
                      type="number"
                      value={formData.year}
                      onChange={(e) =>
                        handleInputChange("year", e.target.value)
                      }
                      className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                      placeholder="2025"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-primary flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Official Use
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="mayor" className="text-card-foreground">
                    Mayor
                  </Label>
                  <Input
                    id="mayor"
                    value={formData.mayor}
                    onChange={(e) => handleInputChange("mayor", e.target.value)}
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="Mayor name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ctcNo" className="text-card-foreground">
                    CTC No.
                  </Label>
                  <Input
                    id="ctcNo"
                    value={formData.ctcNo}
                    onChange={(e) => handleInputChange("ctcNo", e.target.value)}
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="CTC Number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="orNo" className="text-card-foreground">
                    OR No.
                  </Label>
                  <Input
                    id="orNo"
                    value={formData.orNo}
                    onChange={(e) => handleInputChange("orNo", e.target.value)}
                    className="bg-input/30 border-border text-foreground placeholder:text-muted-foreground focus:border-primary"
                    placeholder="OR Number"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-8">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-12 py-3 text-lg rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                  Generating Certificate...
                </div>
              ) : (
                "Generate Certificate"
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
